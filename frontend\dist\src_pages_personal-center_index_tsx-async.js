((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/personal-center/index.tsx'],
{ "src/pages/personal-center/TeamListCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _team = __mako_require__("src/services/team.ts");
var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
var _teamSelectionUtils = __mako_require__("src/utils/teamSelectionUtils.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
const { TextArea } = _antd.Input;
// 响应式布局样式
const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 8px;
    }

    .team-stats-row {
      margin-top: 8px;
    }

    .team-info-wrap {
      gap: 8px !important;
    }
  }

  @media (max-width: 576px) {
    .team-stats-row {
      margin-top: 12px;
    }

    .team-stats-col {
      margin-bottom: 4px;
    }

    .team-info-wrap {
      gap: 6px !important;
    }

    .team-meta-info {
      flex-wrap: wrap;
      gap: 6px !important;
    }

    .team-status-badges {
      flex-wrap: wrap;
      gap: 4px !important;
      margin-top: 4px;
    }
  }

  @media (max-width: 480px) {
    .team-name-text {
      font-size: 14px !important;
    }

    .team-meta-text {
      font-size: 11px !important;
    }

    .team-meta-info {
      gap: 4px !important;
    }

    .team-status-badges {
      gap: 3px !important;
    }
  }
`;
const TeamListCard = ()=>{
    _s();
    // 团队列表状态管理
    const [teams, setTeams] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
    // 创建团队模态框状态
    const [createModalVisible, setCreateModalVisible] = (0, _react.useState)(false);
    const [createLoading, setCreateLoading] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
    // 获取当前Token中的团队信息和用户信息
    const currentTokenTeamId = (0, _tokenUtils.getTeamIdFromCurrentToken)();
    const currentUserId = (0, _tokenUtils.getUserIdFromCurrentToken)();
    const hasTeamInToken = (0, _tokenUtils.hasTeamInCurrentToken)();
    // 判断是否有真正的当前团队：
    // 1. Token中有团队信息（说明用户已经选择过团队）
    // 2. initialState中有团队信息（说明已经获取过团队详情）
    // 3. 两者的团队ID一致（确保状态同步）
    // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）
    const hasRealCurrentTeam = !!(hasTeamInToken && currentTokenTeamId && currentTeam && currentTeam.id === currentTokenTeamId && currentUserId && (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId));
    // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID
    const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;
    // 调试日志
    console.log('TeamListCard 状态调试:', {
        currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
        currentTokenTeamId,
        currentUserId,
        hasTeamInToken,
        hasRealCurrentTeam,
        actualCurrentTeamId,
        hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId) : false,
        initialStateCurrentUser: !!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)
    });
    // 获取团队列表数据
    (0, _react.useEffect)(()=>{
        const fetchTeams = async ()=>{
            try {
                setLoading(true);
                setError(null);
                const teamsData = await _team.TeamService.getUserTeamsWithStats();
                setTeams(teamsData);
            } catch (error) {
                console.error('获取团队列表失败:', error);
                setError('获取团队列表失败');
            } finally{
                setLoading(false);
            }
        };
        // 只有在用户已登录时才获取团队列表
        if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) fetchTeams();
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    // 监听全局状态变化，处理注销等情况
    (0, _react.useEffect)(()=>{
        // 如果用户已注销（currentUser为undefined），清除本地团队列表状态
        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) {
            setTeams([]);
            setError(null);
            setLoading(false);
            setSwitchingTeamId(null);
        }
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    // 监听当前团队状态变化
    (0, _react.useEffect)(()=>{
        console.log('当前团队状态变化:', {
            currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
            actualCurrentTeamId,
            hasRealCurrentTeam
        });
    }, [
        currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
        actualCurrentTeamId,
        hasRealCurrentTeam
    ]);
    // 创建团队处理函数
    const handleCreateTeam = async (values)=>{
        setCreateLoading(true);
        try {
            await _team.TeamService.createTeam(values);
            _antd.message.success('团队创建成功！');
            // 重新获取团队列表
            const teamsData = await _team.TeamService.getUserTeamsWithStats();
            setTeams(teamsData);
            // 关闭模态框并重置表单
            setCreateModalVisible(false);
            form.resetFields();
        } catch (error) {
            console.error('创建团队失败:', error);
            _antd.message.error('创建团队失败，请重试');
        } finally{
            setCreateLoading(false);
        }
    };
    // 团队切换处理函数
    const handleTeamSwitch = async (teamId, teamName)=>{
        // 检查用户是否已登录
        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) {
            _antd.message.error('请先登录');
            return;
        }
        try {
            setSwitchingTeamId(teamId);
            // 如果是当前团队，直接跳转到仪表盘，不需要调用切换API
            if (teamId === actualCurrentTeamId) {
                _antd.message.success(`进入团队：${teamName}`);
                _max.history.push('/dashboard');
                return;
            }
            // 非当前团队，执行切换逻辑
            const response = await _services.AuthService.selectTeam({
                teamId
            });
            // 检查后端返回的团队选择成功标识
            if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                _antd.message.success(`已切换到团队：${teamName}`);
                // 记录用户选择了这个团队
                if (currentUserId) (0, _teamSelectionUtils.recordTeamSelection)(currentUserId, teamId);
                // 由于Token已经更新，路由守卫现在能够正确识别团队信息，可以直接跳转
                // 同时异步更新 initialState 以保持状态同步
                if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) // 异步更新状态，不阻塞跳转
                Promise.all([
                    initialState.fetchUserInfo(),
                    initialState.fetchTeamInfo()
                ]).then(([currentUser, currentTeam])=>{
                    if (currentTeam && currentTeam.id === teamId) setInitialState({
                        ...initialState,
                        currentUser,
                        currentTeam
                    });
                }).catch((error)=>{
                    console.error('更新 initialState 失败:', error);
                });
                // 直接跳转，路由守卫会处理团队验证
                _max.history.push('/dashboard');
            } else {
                console.error('团队切换响应异常，未返回正确的团队信息');
                _antd.message.error('团队切换失败，请重试');
            }
        } catch (error) {
            console.error('团队切换失败:', error);
            // 响应拦截器已经处理了错误消息显示，这里只需要记录日志
            // 如果是网络错误或其他非业务错误，才显示通用错误消息
            if (!error.message || error.message === 'Failed to fetch') _antd.message.error('团队切换失败，请检查网络连接');
        } finally{
            setSwitchingTeamId(null);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("style", {
                dangerouslySetInnerHTML: {
                    __html: styles
                }
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 310,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                className: "dashboard-card",
                style: {
                    borderRadius: 16,
                    boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
                    border: 'none',
                    background: 'linear-gradient(145deg, #ffffff, #f8faff)'
                },
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                    justify: "space-between",
                    align: "center",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 4,
                            style: {
                                margin: 0,
                                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                                fontWeight: 600
                            },
                            children: "团队列表"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 322,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 336,
                                columnNumber: 21
                            }, void 0),
                            onClick: ()=>setCreateModalVisible(true),
                            style: {
                                borderRadius: 8,
                                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                border: 'none',
                                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)'
                            },
                            children: "创建团队"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 334,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 321,
                    columnNumber: 11
                }, void 0),
                children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "团队列表加载失败",
                    description: error,
                    type: "error",
                    showIcon: true,
                    style: {
                        marginBottom: 16
                    }
                }, void 0, false, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 351,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                    spinning: loading,
                    children: !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '40px 20px'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "请先登录以查看团队列表"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 362,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 361,
                        columnNumber: 15
                    }, this) : teams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '40px 20px'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "暂无团队，请先加入或创建团队"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 366,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 365,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                        dataSource: teams,
                        renderItem: (item)=>{
                            var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                    className: "team-item",
                                    style: {
                                        background: actualCurrentTeamId === item.id ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)' : '#fff',
                                        borderRadius: 8,
                                        boxShadow: actualCurrentTeamId === item.id ? '0 2px 8px rgba(24, 144, 255, 0.12)' : '0 1px 4px rgba(0,0,0,0.06)',
                                        width: '100%',
                                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,
                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                        border: actualCurrentTeamId === item.id ? '1px solid #91caff' : '1px solid #f0f0f0',
                                        padding: '12px 16px',
                                        position: 'relative',
                                        overflow: 'hidden'
                                    },
                                    hoverable: true,
                                    onMouseEnter: (e)=>{
                                        if (actualCurrentTeamId !== item.id) {
                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                                        }
                                    },
                                    onMouseLeave: (e)=>{
                                        if (actualCurrentTeamId !== item.id) {
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                                        }
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                        gutter: [
                                            8,
                                            8
                                        ],
                                        align: "middle",
                                        style: {
                                            width: '100%'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 14,
                                                lg: 12,
                                                xl: 14,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    vertical: true,
                                                    gap: 6,
                                                    className: "team-info-wrap",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 8,
                                                            wrap: "wrap",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        cursor: 'pointer',
                                                                        padding: '2px 4px',
                                                                        borderRadius: 4,
                                                                        transition: 'all 0.2s ease',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 6
                                                                    },
                                                                    onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                    onMouseEnter: (e)=>{
                                                                        e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';
                                                                    },
                                                                    onMouseLeave: (e)=>{
                                                                        e.currentTarget.style.background = 'transparent';
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 16,
                                                                                color: actualCurrentTeamId === item.id ? '#1890ff' : '#262626',
                                                                                lineHeight: 1.2
                                                                            },
                                                                            children: item.name
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 445,
                                                                            columnNumber: 33
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {
                                                                            style: {
                                                                                fontSize: 10,
                                                                                color: actualCurrentTeamId === item.id ? '#1890ff' : '#8c8c8c',
                                                                                verticalAlign: 'middle',
                                                                                display: 'inline-flex',
                                                                                alignItems: 'center'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 458,
                                                                            columnNumber: 33
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 423,
                                                                    columnNumber: 31
                                                                }, void 0),
                                                                actualCurrentTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                    style: {
                                                                        background: '#1890ff',
                                                                        color: 'white',
                                                                        padding: '1px 6px',
                                                                        borderRadius: 8,
                                                                        fontSize: 10,
                                                                        fontWeight: 500
                                                                    },
                                                                    children: "当前"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 474,
                                                                    columnNumber: 33
                                                                }, void 0),
                                                                switchingTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    align: "center",
                                                                    gap: 4,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                            size: "small"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 492,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 10,
                                                                                color: '#666'
                                                                            },
                                                                            children: "切换中"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 493,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 491,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 422,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 12,
                                                            wrap: "wrap",
                                                            className: "team-meta-info",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                    title: `团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 4,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                                style: {
                                                                                    color: '#8c8c8c',
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 506,
                                                                                columnNumber: 35
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#8c8c8c'
                                                                                },
                                                                                children: [
                                                                                    "创建: ",
                                                                                    new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 509,
                                                                                columnNumber: 35
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 505,
                                                                        columnNumber: 33
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 502,
                                                                    columnNumber: 31
                                                                }, void 0),
                                                                item.assignedAt && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                    title: `加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 4,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                style: {
                                                                                    color: '#8c8c8c',
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 525,
                                                                                columnNumber: 37
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#8c8c8c'
                                                                                },
                                                                                children: [
                                                                                    "加入: ",
                                                                                    new Date(item.assignedAt).toLocaleDateString('zh-CN')
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 528,
                                                                                columnNumber: 37
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 524,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 521,
                                                                    columnNumber: 33
                                                                }, void 0),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                    title: `团队成员: ${item.memberCount}人`,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                        align: "center",
                                                                        gap: 4,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                                style: {
                                                                                    color: '#8c8c8c',
                                                                                    fontSize: 12
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 543,
                                                                                columnNumber: 35
                                                                            }, void 0),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#8c8c8c'
                                                                                },
                                                                                children: [
                                                                                    item.memberCount,
                                                                                    " 人"
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 546,
                                                                                columnNumber: 35
                                                                            }, void 0)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 542,
                                                                        columnNumber: 33
                                                                    }, void 0)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 539,
                                                                    columnNumber: 31
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 501,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                            align: "center",
                                                            gap: 8,
                                                            wrap: "wrap",
                                                            className: "team-status-badges",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                    style: {
                                                                        background: item.isCreator ? '#722ed1' : '#52c41a',
                                                                        color: 'white',
                                                                        padding: '2px 6px',
                                                                        borderRadius: 8,
                                                                        fontSize: 10,
                                                                        fontWeight: 500,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 2
                                                                    },
                                                                    children: item.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                                style: {
                                                                                    fontSize: 9
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 575,
                                                                                columnNumber: 37
                                                                            }, void 0),
                                                                            "管理员"
                                                                        ]
                                                                    }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                                style: {
                                                                                    fontSize: 9
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 580,
                                                                                columnNumber: 37
                                                                            }, void 0),
                                                                            "成员"
                                                                        ]
                                                                    }, void 0, true)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 558,
                                                                    columnNumber: 31
                                                                }, void 0),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                    style: {
                                                                        background: item.isActive ? '#52c41a' : '#ff4d4f',
                                                                        color: 'white',
                                                                        padding: '2px 6px',
                                                                        borderRadius: 8,
                                                                        fontSize: 10,
                                                                        fontWeight: 500,
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 2
                                                                    },
                                                                    children: item.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                                style: {
                                                                                    fontSize: 9
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 602,
                                                                                columnNumber: 37
                                                                            }, void 0),
                                                                            "启用"
                                                                        ]
                                                                    }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MinusCircleOutlined, {
                                                                                style: {
                                                                                    fontSize: 9
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                                lineNumber: 607,
                                                                                columnNumber: 37
                                                                            }, void 0),
                                                                            "停用"
                                                                        ]
                                                                    }, void 0, true)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 587,
                                                                    columnNumber: 31
                                                                }, void 0)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 556,
                                                            columnNumber: 29
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                    lineNumber: 420,
                                                    columnNumber: 27
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                lineNumber: 419,
                                                columnNumber: 25
                                            }, void 0),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 10,
                                                lg: 12,
                                                xl: 10,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                    gutter: [
                                                        4,
                                                        4
                                                    ],
                                                    justify: {
                                                        xs: 'start',
                                                        md: 'end'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 6,
                                                            sm: 6,
                                                            md: 6,
                                                            lg: 6,
                                                            xl: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    background: '#f0f7ff',
                                                                    border: '1px solid #d9e8ff',
                                                                    borderRadius: 6,
                                                                    padding: '4px 6px',
                                                                    textAlign: 'center',
                                                                    minWidth: '45px'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 1,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                            style: {
                                                                                color: '#1890ff',
                                                                                fontSize: 12
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 635,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#1890ff',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 638,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 8,
                                                                                color: '#666'
                                                                            },
                                                                            children: "车辆"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 648,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 634,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 624,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 623,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 6,
                                                            sm: 6,
                                                            md: 6,
                                                            lg: 6,
                                                            xl: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    background: '#f6ffed',
                                                                    border: '1px solid #d1f0be',
                                                                    borderRadius: 6,
                                                                    padding: '4px 6px',
                                                                    textAlign: 'center',
                                                                    minWidth: '45px'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 1,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                            style: {
                                                                                color: '#52c41a',
                                                                                fontSize: 12
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 668,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#52c41a',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 671,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 8,
                                                                                color: '#666'
                                                                            },
                                                                            children: "人员"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 681,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 667,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 657,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 656,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 6,
                                                            sm: 6,
                                                            md: 6,
                                                            lg: 6,
                                                            xl: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    background: '#fff7e6',
                                                                    border: '1px solid #ffd666',
                                                                    borderRadius: 6,
                                                                    padding: '4px 6px',
                                                                    textAlign: 'center',
                                                                    minWidth: '45px'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 1,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                            style: {
                                                                                color: '#faad14',
                                                                                fontSize: 12
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 701,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#faad14',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 704,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 8,
                                                                                color: '#666'
                                                                            },
                                                                            children: "临期"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 714,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 700,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 690,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 689,
                                                            columnNumber: 29
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                            xs: 6,
                                                            sm: 6,
                                                            md: 6,
                                                            lg: 6,
                                                            xl: 6,
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    background: '#fff1f0',
                                                                    border: '1px solid #ffccc7',
                                                                    borderRadius: 6,
                                                                    padding: '4px 6px',
                                                                    textAlign: 'center',
                                                                    minWidth: '45px'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 1,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                            style: {
                                                                                color: '#ff4d4f',
                                                                                fontSize: 12
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 734,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            strong: true,
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#ff4d4f',
                                                                                lineHeight: 1
                                                                            },
                                                                            children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 737,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 8,
                                                                                color: '#666'
                                                                            },
                                                                            children: "逾期"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 747,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 733,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 723,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 722,
                                                            columnNumber: 29
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                    lineNumber: 618,
                                                    columnNumber: 27
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                lineNumber: 617,
                                                columnNumber: 25
                                            }, void 0)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                        lineNumber: 413,
                                        columnNumber: 23
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 373,
                                    columnNumber: 21
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 372,
                                columnNumber: 19
                            }, void 0);
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 369,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 359,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 312,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                    align: "center",
                    gap: 8,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                            style: {
                                color: '#1890ff'
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 769,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                            children: "创建新团队"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 770,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 768,
                    columnNumber: 11
                }, void 0),
                open: createModalVisible,
                onCancel: ()=>{
                    setCreateModalVisible(false);
                    form.resetFields();
                },
                footer: null,
                width: 500,
                style: {
                    top: 100
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleCreateTeam,
                    autoComplete: "off",
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队名称",
                            name: "name",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称！'
                                },
                                {
                                    max: 100,
                                    message: '团队名称长度不能超过100字符！'
                                },
                                {
                                    min: 2,
                                    message: '团队名称至少需要2个字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称",
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 798,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 789,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            label: "团队描述",
                            name: "description",
                            rules: [
                                {
                                    max: 500,
                                    message: '团队描述长度不能超过500字符！'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                placeholder: "请输入团队描述（可选）",
                                rows: 4,
                                showCount: true,
                                maxLength: 500
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 806,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 801,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            style: {
                                marginBottom: 0,
                                marginTop: 32
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                justify: "end",
                                gap: 12,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setCreateModalVisible(false);
                                            form.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                        lineNumber: 816,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: createLoading,
                                        style: {
                                            background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                                            border: 'none'
                                        },
                                        children: "创建团队"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                        lineNumber: 824,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 815,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 814,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 782,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 766,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(TeamListCard, "OLQwYV09W914HhrfujNR0Qzthjg=", false, function() {
    return [
        _antd.Form.useForm,
        _max.useModel
    ];
});
_c = TeamListCard;
var _default = TeamListCard;
var _c;
$RefreshReg$(_c, "TeamListCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/TodoManagement.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _todo = __mako_require__("src/services/todo.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { TabPane } = _antd.Tabs;
const TodoManagement = (props)=>{
    _s();
    // TODO数据状态管理
    const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
    const [todoStats, setTodoStats] = (0, _react.useState)({
        highPriorityCount: 0,
        mediumPriorityCount: 0,
        lowPriorityCount: 0,
        totalCount: 0,
        completedCount: 0,
        completionPercentage: 0
    });
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    // 待办事项状态管理
    const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
    const [todoForm] = _antd.Form.useForm();
    const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
    // 过滤器状态
    const [activeTab, setActiveTab] = (0, _react.useState)('pending');
    const [searchText, setSearchText] = (0, _react.useState)('');
    // 获取TODO数据
    (0, _react.useEffect)(()=>{
        const fetchTodoData = async ()=>{
            try {
                setLoading(true);
                setError(null);
                console.log('TodoManagement: 开始获取TODO数据');
                // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                    console.error('获取TODO列表失败:', error);
                    return [];
                });
                const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                    console.error('获取TODO统计失败:', error);
                    return {
                        highPriorityCount: 0,
                        mediumPriorityCount: 0,
                        lowPriorityCount: 0,
                        totalCount: 0,
                        completedCount: 0,
                        completionPercentage: 0
                    };
                });
                const [todos, stats] = await Promise.all([
                    todosPromise,
                    statsPromise
                ]);
                console.log('TodoManagement: 获取到TODO列表:', todos);
                console.log('TodoManagement: 获取到统计数据:', stats);
                setPersonalTasks(todos);
                setTodoStats(stats);
            } catch (error) {
                console.error('获取TODO数据时发生未知错误:', error);
                setError('获取TODO数据失败，请刷新页面重试');
            } finally{
                setLoading(false);
            }
        };
        fetchTodoData();
    }, []);
    // 根据激活的标签和搜索文本过滤任务
    const filteredPersonalTasks = (personalTasks || []).filter((task)=>{
        // 根据标签过滤
        if (activeTab === 'pending' && task.status === 1) return false;
        if (activeTab === 'completed' && task.status === 0) return false;
        // 根据搜索文本过滤
        if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
        return true;
    });
    // 处理待办事项操作
    const handleToggleTodoStatus = async (id)=>{
        try {
            const task = personalTasks.find((t)=>t.id === id);
            if (!task) {
                _antd.message.error('任务不存在');
                return;
            }
            const newStatus = task.status === 0 ? 1 : 0;
            console.log(`TodoManagement: 更新任务状态 ${id} -> ${newStatus}`);
            await _todo.TodoService.updateTodo(id, {
                status: newStatus
            });
            // 更新本地状态
            setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                    ...task,
                    status: newStatus
                } : task));
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            // 统计数据刷新失败不影响主要操作
            }
            _antd.message.success(newStatus === 1 ? '任务已完成' : '任务已标记为未完成');
        } catch (error) {
            console.error('更新任务状态失败:', error);
            _antd.message.error('更新任务状态失败，请稍后重试');
        }
    };
    const handleAddOrUpdateTodo = async (values)=>{
        try {
            console.log('TodoManagement: 保存任务', {
                editingTodoId,
                values
            });
            if (editingTodoId) {
                // 更新现有待办事项
                const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                _antd.message.success('任务更新成功');
            } else {
                // 添加新待办事项
                const newTodo = await _todo.TodoService.createTodo({
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks([
                    newTodo,
                    ...personalTasks
                ]);
                _antd.message.success('任务创建成功');
            }
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            // 统计数据刷新失败不影响主要操作
            }
            // 重置表单并关闭模态框
            setTodoModalVisible(false);
            setEditingTodoId(null);
            todoForm.resetFields();
        } catch (error) {
            console.error('保存任务失败:', error);
            const action = editingTodoId ? '更新' : '创建';
            _antd.message.error(`${action}任务失败，请检查网络连接后重试`);
        }
    };
    const handleDeleteTodo = async (id)=>{
        try {
            console.log('TodoManagement: 删除任务', id);
            await _todo.TodoService.deleteTodo(id);
            setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
                console.error('刷新统计数据失败:', statsError);
            // 统计数据刷新失败不影响主要操作
            }
            _antd.message.success('任务删除成功');
        } catch (error) {
            console.error('删除任务失败:', error);
            _antd.message.error('删除任务失败，请稍后重试');
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        className: "dashboard-card",
        style: {
            borderRadius: 12,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            border: 'none',
            background: 'linear-gradient(145deg, #ffffff, #f5f8ff)'
        },
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
            justify: "space-between",
            align: "center",
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                strong: true,
                children: "待办事项"
            }, void 0, false, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 251,
                columnNumber: 11
            }, void 0)
        }, void 0, false, {
            fileName: "src/pages/personal-center/TodoManagement.tsx",
            lineNumber: 250,
            columnNumber: 9
        }, void 0),
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: '12px 16px',
                    background: '#fafbfc',
                    borderRadius: 8,
                    border: '1px solid #f0f0f0'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        12
                    ],
                    align: "middle",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 8,
                            lg: 8,
                            xl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                align: "center",
                                gap: 12,
                                style: {
                                    width: '100%'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                        placeholder: "搜索任务...",
                                        allowClear: true,
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 273,
                                            columnNumber: 25
                                        }, void 0),
                                        value: searchText,
                                        onChange: (e)=>setSearchText(e.target.value),
                                        style: {
                                            flex: 1
                                        },
                                        size: "middle"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 270,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 282,
                                            columnNumber: 23
                                        }, void 0),
                                        onClick: ()=>{
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                            setTodoModalVisible(true);
                                        },
                                        style: {
                                            background: '#1890ff',
                                            borderColor: '#1890ff',
                                            boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                                            fontWeight: 500,
                                            minWidth: 80
                                        },
                                        size: "middle",
                                        children: "新增"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 280,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 269,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 268,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 8,
                            lg: 8,
                            xl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                align: "center",
                                justify: "center",
                                wrap: "wrap",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    size: 12,
                                    wrap: true,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: `高优先级任务: ${todoStats.highPriorityCount}个`,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 8,
                                                            height: 8,
                                                            borderRadius: '50%',
                                                            background: '#ff4d4f'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 310,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            fontWeight: 500,
                                                            color: '#262626'
                                                        },
                                                        children: [
                                                            "高: ",
                                                            todoStats.highPriorityCount
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 318,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 309,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 306,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: `中优先级任务: ${todoStats.mediumPriorityCount}个`,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 8,
                                                            height: 8,
                                                            borderRadius: '50%',
                                                            background: '#faad14'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 334,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            fontWeight: 500,
                                                            color: '#262626'
                                                        },
                                                        children: [
                                                            "中: ",
                                                            todoStats.mediumPriorityCount
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 342,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 333,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 330,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                            title: `低优先级任务: ${todoStats.lowPriorityCount}个`,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            width: 8,
                                                            height: 8,
                                                            borderRadius: '50%',
                                                            background: '#52c41a'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 358,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            fontWeight: 500,
                                                            color: '#262626'
                                                        },
                                                        children: [
                                                            "低: ",
                                                            todoStats.lowPriorityCount
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 366,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 357,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 354,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 305,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 304,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 303,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 24,
                            md: 8,
                            lg: 8,
                            xl: 8,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                align: "center",
                                justify: "center",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                    title: `完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                        align: "center",
                                        gap: 6,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    fontSize: 12,
                                                    fontWeight: 500,
                                                    color: '#595959'
                                                },
                                                children: "完成率:"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 388,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                percent: todoStats.completionPercentage,
                                                size: "small",
                                                style: {
                                                    width: 80
                                                },
                                                strokeColor: "#52c41a",
                                                showInfo: false
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 393,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                style: {
                                                    fontSize: 12,
                                                    fontWeight: 600,
                                                    color: '#262626'
                                                },
                                                children: [
                                                    todoStats.completionPercentage,
                                                    "%"
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 400,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 387,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 384,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 383,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 382,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                    lineNumber: 266,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 256,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: (key)=>setActiveTab(key),
                size: "middle",
                style: {
                    marginBottom: 8
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "全部"
                    }, "all", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 419,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "待处理"
                    }, "pending", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 420,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "已完成"
                    }, "completed", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 421,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 413,
                columnNumber: 7
            }, this),
            error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "TODO数据加载失败",
                description: error,
                type: "error",
                showIcon: true,
                style: {
                    marginBottom: 16
                }
            }, void 0, false, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 426,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                spinning: loading,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                        dataSource: filteredPersonalTasks,
                        renderItem: (item)=>{
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                className: "todo-item",
                                style: {
                                    padding: '10px 16px',
                                    marginBottom: 12,
                                    borderRadius: 8,
                                    background: '#fff',
                                    opacity: item.status === 1 ? 0.7 : 1,
                                    borderLeft: `3px solid ${item.status === 1 ? '#52c41a' : item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`,
                                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 12,
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            align: "center",
                                            children: [
                                                item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    justify: "center",
                                                    style: {
                                                        width: 22,
                                                        height: 22,
                                                        borderRadius: '50%',
                                                        background: '#52c41a'
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                        style: {
                                                            color: '#fff',
                                                            fontSize: 12
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 473,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 463,
                                                    columnNumber: 25
                                                }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 18,
                                                        height: 18,
                                                        borderRadius: '50%',
                                                        border: `2px solid ${item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 478,
                                                    columnNumber: 25
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 2,
                                                        height: 24,
                                                        background: '#f0f0f0',
                                                        marginTop: 4
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 494,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 461,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            style: {
                                                flex: 1
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: item.priority === 3 ? 500 : 'normal',
                                                        textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                        color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                    },
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 506,
                                                    columnNumber: 23
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    align: "center",
                                                    size: 6,
                                                    style: {
                                                        marginTop: 4
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                            style: {
                                                                fontSize: 12,
                                                                color: '#8c8c8c'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 520,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            style: {
                                                                fontSize: 12
                                                            },
                                                            children: [
                                                                "创建于:",
                                                                ' ',
                                                                new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 526,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 519,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 505,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                            trigger: [
                                                'click'
                                            ],
                                            menu: {
                                                items: [
                                                    {
                                                        key: 'complete',
                                                        label: item.status === 1 ? '标记未完成' : '标记完成',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                fontSize: 14
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 543,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'edit',
                                                        label: '编辑任务',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                            style: {
                                                                color: '#8c8c8c'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 555,
                                                            columnNumber: 35
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'delete',
                                                        label: '删除任务',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                            style: {
                                                                color: '#ff4d4f'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 561,
                                                            columnNumber: 31
                                                        }, void 0),
                                                        danger: true
                                                    }
                                                ],
                                                onClick: ({ key })=>{
                                                    if (key === 'complete') handleToggleTodoStatus(item.id);
                                                    else if (key === 'edit') {
                                                        setEditingTodoId(item.id);
                                                        todoForm.setFieldsValue({
                                                            name: item.title,
                                                            priority: item.priority
                                                        });
                                                        setTodoModalVisible(true);
                                                    } else if (key === 'delete') handleDeleteTodo(item.id);
                                                }
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                size: "small",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 585,
                                                    columnNumber: 31
                                                }, void 0),
                                                style: {
                                                    width: 32,
                                                    height: 32
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 582,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 534,
                                            columnNumber: 21
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 459,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 439,
                                columnNumber: 17
                            }, void 0);
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 435,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                        title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                        open: todoModalVisible,
                        onCancel: ()=>{
                            setTodoModalVisible(false);
                            todoForm.resetFields();
                        },
                        onOk: ()=>{
                            todoForm.submit();
                        },
                        centered: true,
                        destroyOnClose: true,
                        footer: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                onClick: ()=>setTodoModalVisible(false),
                                children: "取消"
                            }, "cancel", false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 609,
                                columnNumber: 15
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "primary",
                                onClick: ()=>{
                                    todoForm.submit();
                                },
                                style: {
                                    background: '#1890ff',
                                    borderColor: '#1890ff',
                                    boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                },
                                children: editingTodoId ? '更新任务' : '创建任务'
                            }, "submit", false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 612,
                                columnNumber: 15
                            }, void 0)
                        ],
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: todoForm,
                            layout: "vertical",
                            onFinish: handleAddOrUpdateTodo,
                            autoComplete: "off",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "name",
                                    label: "任务名称",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入任务名称'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入任务名称",
                                        size: "large",
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 639,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 634,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "priority",
                                    label: "优先级",
                                    initialValue: 2,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择优先级'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        size: "large",
                                        options: [
                                            {
                                                value: 3,
                                                label: '高优先级'
                                            },
                                            {
                                                value: 2,
                                                label: '中优先级'
                                            },
                                            {
                                                value: 1,
                                                label: '低优先级'
                                            }
                                        ],
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 652,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 646,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 628,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 596,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 434,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/TodoManagement.tsx",
        lineNumber: 241,
        columnNumber: 5
    }, this);
};
_s(TodoManagement, "BRDE78r/O6qdRZ096tTY2qeEVcA=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TodoManagement;
var _default = TodoManagement;
var _c;
$RefreshReg$(_c, "TodoManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/UserProfileCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _user = __mako_require__("src/services/user.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UserProfileCard = ()=>{
    _s();
    // 用户详细信息状态
    const [userInfo, setUserInfo] = (0, _react.useState)({
        name: '',
        position: '',
        email: '',
        telephone: '',
        registerDate: '',
        lastLoginTime: '',
        lastLoginTeam: '',
        teamCount: 0,
        avatar: ''
    });
    const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
    const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
    // 个人统计数据状态
    const [personalStats, setPersonalStats] = (0, _react.useState)({
        vehicles: 0,
        personnel: 0,
        warnings: 0,
        alerts: 0
    });
    const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
    const [statsError, setStatsError] = (0, _react.useState)(null);
    // 状态管理
    // 获取用户数据
    (0, _react.useEffect)(()=>{
        console.log('UserProfileCard: useEffect 开始执行');
        const fetchUserData = async ()=>{
            try {
                console.log('UserProfileCard: 开始获取用户数据');
                // 分别获取用户详细信息和统计数据，避免一个失败影响另一个
                const userDetailPromise = _user.UserService.getUserProfileDetail().catch((error)=>{
                    console.error('获取用户详细信息失败:', error);
                    setUserInfoError('获取用户详细信息失败，请稍后重试');
                    return null;
                });
                const statsPromise = _user.UserService.getUserPersonalStats().catch((error)=>{
                    console.error('获取统计数据失败:', error);
                    setStatsError('获取统计数据失败，请稍后重试');
                    return null;
                });
                const [userDetail, stats] = await Promise.all([
                    userDetailPromise,
                    statsPromise
                ]);
                if (userDetail) {
                    console.log('UserProfileCard: 获取到用户详细信息:', userDetail);
                    setUserInfo(userDetail);
                    setUserInfoError(null);
                }
                if (stats) {
                    console.log('UserProfileCard: 获取到统计数据:', stats);
                    setPersonalStats(stats);
                    setStatsError(null);
                }
            } catch (error) {
                console.error('获取用户数据时发生未知错误:', error);
                setUserInfoError('获取用户数据失败，请刷新页面重试');
                setStatsError('获取统计数据失败，请刷新页面重试');
            } finally{
                setUserInfoLoading(false);
                setStatsLoading(false);
            }
        };
        fetchUserData();
    }, []);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
            message: "用户信息加载失败",
            description: userInfoError,
            type: "error",
            showIcon: true,
            style: {
                marginBottom: 24
            }
        }, void 0, false, {
            fileName: "src/pages/personal-center/UserProfileCard.tsx",
            lineNumber: 115,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
            spinning: userInfoLoading,
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                style: {
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: 16,
                    color: 'white',
                    position: 'relative',
                    overflow: 'hidden',
                    minHeight: 140,
                    border: 'none'
                },
                styles: {
                    body: {
                        padding: '16px 16px 16px 16px',
                        height: '100%'
                    }
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            position: 'absolute',
                            top: -25,
                            right: -25,
                            width: 100,
                            height: 100,
                            background: 'rgba(255,255,255,0.1)',
                            borderRadius: '50%'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                        lineNumber: 143,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            position: 'absolute',
                            bottom: -30,
                            left: -30,
                            width: 80,
                            height: 80,
                            background: 'rgba(255,255,255,0.05)',
                            borderRadius: '50%'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                        lineNumber: 154,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            position: 'absolute',
                            top: '50%',
                            right: '20%',
                            width: 60,
                            height: 60,
                            background: 'rgba(255,255,255,0.03)',
                            borderRadius: '50%',
                            transform: 'translateY(-50%)'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                        lineNumber: 165,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            16,
                            12
                        ],
                        align: "middle",
                        style: {
                            position: 'relative',
                            zIndex: 1,
                            width: '100%',
                            minHeight: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 8,
                                lg: 7,
                                xl: 6,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    style: {
                                        minHeight: '80px'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                            size: 64,
                                            shape: "square",
                                            style: {
                                                backgroundColor: 'rgba(255,255,255,0.2)',
                                                marginRight: 20,
                                                fontSize: 24,
                                                fontWeight: 600,
                                                border: '2px solid rgba(255,255,255,0.3)'
                                            },
                                            children: userInfo.name ? userInfo.name.charAt(0).toUpperCase() : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                lineNumber: 207,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                            lineNumber: 193,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            direction: "vertical",
                                            size: 4,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 3,
                                                    style: {
                                                        margin: 0,
                                                        color: 'white',
                                                        fontSize: 22,
                                                        fontWeight: 600
                                                    },
                                                    children: userInfo.name || '加载中...'
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                    lineNumber: 213,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    direction: "vertical",
                                                    size: 4,
                                                    children: [
                                                        userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: 6,
                                                            align: "center",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: 'rgba(255,255,255,0.9)'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 229,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        color: 'rgba(255,255,255,0.9)',
                                                                        fontSize: 12
                                                                    },
                                                                    children: userInfo.email
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 235,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 228,
                                                            columnNumber: 25
                                                        }, this),
                                                        userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            size: 6,
                                                            align: "center",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                    style: {
                                                                        fontSize: 13,
                                                                        color: 'rgba(255,255,255,0.9)'
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 247,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                    style: {
                                                                        color: 'rgba(255,255,255,0.9)',
                                                                        fontSize: 12
                                                                    },
                                                                    children: userInfo.telephone
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 253,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                    lineNumber: 226,
                                                    columnNumber: 21
                                                }, this),
                                                userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 13,
                                                        color: 'rgba(255,255,255,0.8)',
                                                        fontWeight: 500
                                                    },
                                                    children: [
                                                        "注册于 ",
                                                        userInfo.registerDate
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                            lineNumber: 212,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                    lineNumber: 191,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                lineNumber: 190,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 8,
                                lg: 10,
                                xl: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    vertical: true,
                                    justify: "center",
                                    style: {
                                        minHeight: '80px',
                                        textAlign: 'center',
                                        padding: '8px 0'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                            align: "center",
                                            style: {
                                                justifyContent: 'center',
                                                marginBottom: 16
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BarChartOutlined, {
                                                    style: {
                                                        fontSize: 16,
                                                        color: 'rgba(255,255,255,0.9)'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                    lineNumber: 300,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        color: 'rgba(255,255,255,0.9)',
                                                        fontSize: 14,
                                                        fontWeight: 600
                                                    },
                                                    children: "数据概览"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                    lineNumber: 306,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                            lineNumber: 293,
                                            columnNumber: 19
                                        }, this),
                                        statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 12,
                                                color: 'rgba(255,255,255,0.8)'
                                            },
                                            children: "数据加载失败"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                            lineNumber: 319,
                                            columnNumber: 21
                                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                            spinning: statsLoading,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    4,
                                                    8
                                                ],
                                                justify: "center",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 16,
                                                                        fontWeight: 700,
                                                                        color: 'white',
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.vehicles
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 329,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: 'rgba(255,255,255,0.8)',
                                                                        marginTop: 2
                                                                    },
                                                                    children: "车辆"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 339,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 328,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 327,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 16,
                                                                        fontWeight: 700,
                                                                        color: 'white',
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.personnel
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 352,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: 'rgba(255,255,255,0.8)',
                                                                        marginTop: 2
                                                                    },
                                                                    children: "人员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 362,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 351,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 350,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 16,
                                                                        fontWeight: 700,
                                                                        color: 'white',
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.warnings
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 375,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: 'rgba(255,255,255,0.8)',
                                                                        marginTop: 2
                                                                    },
                                                                    children: "预警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 385,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 374,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 373,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                textAlign: 'center'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 16,
                                                                        fontWeight: 700,
                                                                        color: 'white',
                                                                        lineHeight: 1
                                                                    },
                                                                    children: personalStats.alerts
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 398,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 11,
                                                                        color: 'rgba(255,255,255,0.8)',
                                                                        marginTop: 2
                                                                    },
                                                                    children: "告警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                                    lineNumber: 408,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                            lineNumber: 397,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 396,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                lineNumber: 326,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                            lineNumber: 325,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                    lineNumber: 283,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                lineNumber: 282,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 8,
                                lg: 7,
                                xl: 6,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    vertical: true,
                                    justify: "center",
                                    style: {
                                        minHeight: '80px',
                                        padding: '8px 0'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        size: 10,
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: 'rgba(255,255,255,0.8)',
                                                            fontWeight: 500
                                                        },
                                                        children: "最后登录时间"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 434,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            color: 'white',
                                                            fontWeight: 600,
                                                            lineHeight: 1.3
                                                        },
                                                        children: userInfo.lastLoginTime || '暂无记录'
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 443,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                lineNumber: 433,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 4,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 12,
                                                            color: 'rgba(255,255,255,0.8)',
                                                            fontWeight: 500
                                                        },
                                                        children: "最后登录团队"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 455,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        style: {
                                                            fontSize: 14,
                                                            color: 'white',
                                                            fontWeight: 600,
                                                            lineHeight: 1.3
                                                        },
                                                        children: userInfo.lastLoginTeam || '暂无记录'
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                        lineNumber: 464,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                                lineNumber: 454,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                        lineNumber: 432,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                    lineNumber: 427,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                                lineNumber: 426,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UserProfileCard.tsx",
                        lineNumber: 179,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/UserProfileCard.tsx",
                lineNumber: 125,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "src/pages/personal-center/UserProfileCard.tsx",
            lineNumber: 123,
            columnNumber: 9
        }, this)
    }, void 0, false);
};
_s(UserProfileCard, "MHc6SoAKLuRPufZ4cDmBBWQcPB0=");
_c = UserProfileCard;
var _default = UserProfileCard;
var _c;
$RefreshReg$(_c, "UserProfileCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
var _UserProfileCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UserProfileCard.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const PersonalCenterPage = ()=>{
    _s();
    const { initialState, loading } = (0, _max.useModel)('@@initialState');
    // 如果正在加载，显示加载状态
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            minHeight: '100vh',
            background: '#f5f8ff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 24,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginLeft: 16
                },
                children: "正在加载用户信息..."
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/index.tsx",
        lineNumber: 15,
        columnNumber: 7
    }, this);
    // 如果用户未登录，跳转到登录页
    (0, _react.useEffect)(()=>{
        if (!loading && !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) _max.history.push('/user/login');
    }, [
        loading,
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    minHeight: '100vh',
                    background: '#f5f8ff',
                    padding: '12px 12px 24px 12px'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    style: {
                        width: '100%',
                        minHeight: 'calc(100vh - 48px)',
                        borderRadius: '12px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                    },
                    styles: {
                        body: {
                            padding: '24px'
                        }
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            16,
                            16
                        ],
                        style: {
                            margin: 0
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                style: {
                                    marginBottom: 8
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileCard.default, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 63,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 62,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 24,
                                lg: 12,
                                xl: 12,
                                xxl: 12,
                                style: {
                                    marginBottom: 8
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 67,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 24,
                                lg: 12,
                                xl: 12,
                                xxl: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 80,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 47,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 88,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true);
};
_s(PersonalCenterPage, "G/2lp/LTMe9ZvmWNgegOr1tze1g=", false, function() {
    return [
        _max.useModel
    ];
});
_c = PersonalCenterPage;
var _default = PersonalCenterPage;
var _c;
$RefreshReg$(_c, "PersonalCenterPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/todo.ts": function (module, exports, __mako_require__){
/**
 * TODO服务
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "TodoService", {
    enumerable: true,
    get: function() {
        return TodoService;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
class TodoService {
    /**
   * 获取用户的TODO列表
   */ static async getUserTodos() {
        const response = await _request.apiRequest.get('/todos');
        return response.data;
    }
    /**
   * 创建TODO
   */ static async createTodo(request) {
        const response = await _request.apiRequest.post('/todos', request);
        return response.data;
    }
    /**
   * 更新TODO
   */ static async updateTodo(id, request) {
        const response = await _request.apiRequest.put(`/todos/${id}`, request);
        return response.data;
    }
    /**
   * 删除TODO
   */ static async deleteTodo(id) {
        await _request.apiRequest.delete(`/todos/${id}`);
    }
    /**
   * 获取TODO统计信息
   */ static async getTodoStats() {
        const response = await _request.apiRequest.get('/todos/stats');
        return response.data;
    }
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/teamSelectionUtils.ts": function (module, exports, __mako_require__){
/**
 * 团队选择状态管理工具函数
 * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态
 */ // 团队选择历史的本地存储键
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    clearUserTeamSelectionHistory: function() {
        return clearUserTeamSelectionHistory;
    },
    getAllTeamSelectionKeys: function() {
        return getAllTeamSelectionKeys;
    },
    getUserTeamSelectionHistory: function() {
        return getUserTeamSelectionHistory;
    },
    hasUserSelectedTeam: function() {
        return hasUserSelectedTeam;
    },
    recordTeamSelection: function() {
        return recordTeamSelection;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const TEAM_SELECTION_KEY = 'user_team_selection_history';
const getUserTeamSelectionHistory = (userId)=>{
    try {
        const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);
        if (history) return new Set(JSON.parse(history));
    } catch (error) {
        console.error('获取团队选择历史失败:', error);
    }
    return new Set();
};
const recordTeamSelection = (userId, teamId)=>{
    try {
        const history = getUserTeamSelectionHistory(userId);
        history.add(teamId);
        localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([
            ...history
        ]));
        console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);
    } catch (error) {
        console.error('记录团队选择历史失败:', error);
    }
};
const hasUserSelectedTeam = (userId, teamId)=>{
    const history = getUserTeamSelectionHistory(userId);
    return history.has(teamId);
};
const clearUserTeamSelectionHistory = (userId)=>{
    try {
        localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);
        console.log(`清除用户${userId}的团队选择历史`);
    } catch (error) {
        console.error('清除团队选择历史失败:', error);
    }
};
const getAllTeamSelectionKeys = ()=>{
    const keys = [];
    for(let i = 0; i < localStorage.length; i++){
        const key = localStorage.key(i);
        if (key && key.startsWith(TEAM_SELECTION_KEY)) keys.push(key);
    }
    return keys;
};
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_personal-center_index_tsx-async.js.map